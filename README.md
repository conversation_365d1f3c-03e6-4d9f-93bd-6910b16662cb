# Windows Live Caption 控制工具使用手册

## 简介

`win-offline-asr.py` 是一个用于控制 Windows Live Caption（实时字幕）应用程序的命令行工具。它提供了启动/停止应用、控制窗口显示、获取字幕内容、设置语言等功能。

## 系统要求

- Windows 10/11 系统
- Python 3.6+
- 已安装的依赖包：
  ```bash
  pip install psutil pywinauto requests
  ```
- Windows Live Caption 应用程序

## 基本用法

```bash
python win-offline-asr.py <命令> [选项]
```

## 命令列表

### 1. 应用程序控制

#### 启动 Live Caption
```bash
python win-offline-asr.py start
```
**功能**：启动 Live Caption 应用程序并连接到窗口
**返回**：JSON 格式的成功/失败状态
```json
{"success": true}
```

#### 停止 Live Caption
```bash
python win-offline-asr.py stop
```
**功能**：强制终止 Live Caption 进程
**返回**：JSON 格式的成功/失败状态

### 2. 窗口控制

#### 隐藏窗口
```bash
python win-offline-asr.py hide
```
**功能**：最小化 Live Caption 窗口并从任务栏隐藏
**返回**：JSON 格式的成功/失败状态

#### 显示窗口
```bash
python win-offline-asr.py show
```
**功能**：恢复显示 Live Caption 窗口
**返回**：JSON 格式的成功/失败状态

### 3. 字幕内容获取

#### 获取当前字幕
```bash
python win-offline-asr.py get_caption
```
**功能**：获取当前显示的字幕文本
**返回**：JSON 格式的字幕内容
```json
{"caption": "当前字幕内容"}
```

#### 实时监控字幕
```bash
python win-offline-asr.py get_caption --watch
```
**功能**：持续监控字幕变化，实时输出新的字幕内容
**选项**：
- `--watch`：启用实时监控模式
**输出**：每当字幕更新时输出 JSON 格式的字幕内容

### 4. 语言设置

#### 设置识别语言
```bash
python win-offline-asr.py set_language --lang "语言前缀"
```
**功能**：设置 Live Caption 的语音识别语言
**选项**：
- `--lang`：语言前缀（必需），例如：
  - `"简体中文"`
  - `"日语"`
  - `"English"`
  - `"한국어"`
  - `"Français"`

**示例**：
```bash
python win-offline-asr.py set_language --lang "简体中文"
python win-offline-asr.py set_language --lang "日语"
python win-offline-asr.py set_language --lang "English"
```

**返回**：JSON 格式的成功/失败状态
```json
{"success": true}
```

#### 获取可用语言列表
```bash
python win-offline-asr.py list_languages [选项]
```
**功能**：获取所有可用的语音识别语言列表，默认包含英文翻译

**选项**：
- `--fast`：使用快速滚动方法获取语言列表
- `--no-translate`：不翻译语言名称，只返回原始语言列表
- `--api-key`：指定微软翻译API密钥（可选，有默认密钥）

**示例**：
```bash
# 获取语言列表并翻译（默认行为）
python win-offline-asr.py list_languages

# 快速获取并翻译
python win-offline-asr.py list_languages --fast

# 只获取原始语言列表，不翻译
python win-offline-asr.py list_languages --no-translate

# 使用自定义API密钥
python win-offline-asr.py list_languages --api-key "your-api-key"
```

**返回**：
- 带翻译的返回格式：
```json
{
  "languages": ["简体中文 (中国)", "日本語 (日本)", "English (United States)"],
  "translations": {
    "简体中文 (中国)": "Simplified Chinese (China)",
    "日本語 (日本)": "Japanese (Japan)",
    "English (United States)": "English (United States)"
  }
}
```

- 不翻译的返回格式：
```json
{
  "languages": ["简体中文 (中国)", "日本語 (日本)", "English (United States)"]
}
```

### 5. 音频设置

#### 启用麦克风音频
```bash
python win-offline-asr.py enable_mic_audio
```
**功能**：在首选项中启用"包含麦克风音频"选项
**返回**：JSON 格式的成功/失败状态

#### 禁用麦克风音频
```bash
python win-offline-asr.py disable_mic_audio
```
**功能**：在首选项中禁用"包含麦克风音频"选项
**返回**：JSON 格式的成功/失败状态

## 使用示例

### 完整工作流程示例

```bash
# 1. 启动 Live Caption
python win-offline-asr.py start

# 2. 设置为中文识别
python win-offline-asr.py set_language --lang "简体中文"

# 3. 启用麦克风音频
python win-offline-asr.py enable_mic_audio

# 4. 获取当前字幕
python win-offline-asr.py get_caption

# 5. 实时监控字幕（Ctrl+C 停止）
python win-offline-asr.py get_caption --watch

# 6. 隐藏窗口
python win-offline-asr.py hide

# 7. 获取可用语言列表
python win-offline-asr.py list_languages

# 8. 停止应用
python win-offline-asr.py stop
```

### 批处理脚本示例

创建 `setup_livecaption.bat`：
```batch
@echo off
echo 启动 Live Caption...
python win-offline-asr.py start
timeout /t 2

echo 设置语言为简体中文...
python win-offline-asr.py set_language --lang "简体中文"

echo 启用麦克风音频...
python win-offline-asr.py enable_mic_audio

echo 隐藏窗口...
python win-offline-asr.py hide

echo Live Caption 设置完成！
```

## 错误处理

所有命令都会返回 JSON 格式的响应。如果操作失败，`success` 字段将为 `false`：

```json
{"success": false}
```

常见错误原因：
1. Live Caption 应用程序未运行
2. 无法连接到应用程序窗口
3. 界面元素定位失败
4. 网络连接问题（翻译功能）

## 注意事项

1. **权限要求**：脚本需要足够的权限来控制其他应用程序
2. **应用程序状态**：某些操作需要 Live Caption 处于特定状态
3. **界面语言**：脚本支持中英文界面的 Live Caption
4. **网络连接**：语言翻译功能需要互联网连接
5. **API限制**：翻译功能使用微软翻译API，可能有使用限制

## 故障排除

### Live Caption 无法启动
- 确保系统已安装 Live Caption 应用
- 检查应用程序路径是否正确
- 以管理员权限运行脚本

### 语言设置失败
- 确保输入的语言前缀正确
- 使用 `list_languages` 命令查看可用语言
- 检查 Live Caption 是否正在运行

### 翻译功能失败
- 检查网络连接
- 验证API密钥是否有效
- 使用 `--no-translate` 选项跳过翻译

## 高级用法

### 编程集成

可以在 Python 脚本中直接调用相关函数：

```python
import subprocess
import json

def get_live_caption():
    """获取当前字幕"""
    result = subprocess.run([
        'python', 'win-offline-asr.py', 'get_caption'
    ], capture_output=True, text=True)

    if result.returncode == 0:
        data = json.loads(result.stdout)
        return data.get('caption')
    return None

def set_language(lang):
    """设置语言"""
    result = subprocess.run([
        'python', 'win-offline-asr.py', 'set_language', '--lang', lang
    ], capture_output=True, text=True)

    if result.returncode == 0:
        data = json.loads(result.stdout)
        return data.get('success', False)
    return False

# 使用示例
caption = get_live_caption()
print(f"当前字幕: {caption}")

success = set_language("简体中文")
print(f"语言设置: {'成功' if success else '失败'}")
```

### 配置文件支持

创建 `config.json` 配置文件：

```json
{
  "default_language": "简体中文",
  "auto_hide": true,
  "enable_mic": true,
  "api_key": "your-custom-api-key",
  "monitor_interval": 0.5
}
```

### 日志记录

启用详细日志输出：

```bash
# 重定向输出到日志文件
python win-offline-asr.py get_caption --watch > captions.log 2>&1

# 实时查看日志
python win-offline-asr.py get_caption --watch | tee captions.log
```

## API 参考

### 翻译 API 配置

默认使用的微软翻译API配置：
- **端点**：`https://api.cognitive.microsofttranslator.com/translate`
- **API版本**：3.0
- **区域**：centralus
- **支持语言**：自动检测到英文

### 返回值格式

所有命令的返回值都是标准的 JSON 格式：

#### 成功响应
```json
{
  "success": true,
  "data": "相关数据（可选）"
}
```

#### 失败响应
```json
{
  "success": false,
  "error": "错误信息（可选）"
}
```

#### 字幕响应
```json
{
  "caption": "字幕文本内容"
}
```

#### 语言列表响应
```json
{
  "languages": ["语言1", "语言2", "..."],
  "translations": {
    "语言1": "Language 1",
    "语言2": "Language 2"
  }
}
```

## 性能优化

### 快速模式
使用 `--fast` 选项可以提高语言列表获取速度：
```bash
python win-offline-asr.py list_languages --fast
```

### 缓存机制
脚本内置了以下缓存机制：
- 语言列表缓存（避免重复获取）
- 翻译结果缓存（减少API调用）
- 窗口连接缓存（提高响应速度）

### 批量操作
可以创建批处理脚本进行批量操作：

```bash
# 批量设置多种语言并测试
for lang in "简体中文" "日语" "English" "한국어"; do
    echo "设置语言: $lang"
    python win-offline-asr.py set_language --lang "$lang"
    sleep 2
    python win-offline-asr.py get_caption
    echo "---"
done
```

## 技术支持

### 常见问题解答

**Q: 为什么 `set_language` 返回 false？**
A: 可能的原因：
- Live Caption 未运行
- 语言前缀不正确
- 目标语言在列表中但需要滚动才能找到
- 界面语言不匹配

**Q: 翻译功能不工作怎么办？**
A: 检查以下项目：
- 网络连接是否正常
- API密钥是否有效
- 是否超出API调用限制
- 使用 `--no-translate` 跳过翻译

**Q: 如何获取准确的语言前缀？**
A: 使用以下命令查看所有可用语言：
```bash
python win-offline-asr.py list_languages --no-translate
```

### 调试模式

在脚本中添加调试输出：
```python
# 在函数开头添加
print(f"调试: 正在执行 {function_name}")
```

### 系统要求检查

运行前检查系统环境：
```bash
# 检查 Python 版本
python --version

# 检查依赖包
pip list | grep -E "(psutil|pywinauto|requests)"

# 检查 Live Caption 进程
tasklist | findstr LiveCaptions
```

如遇问题，请检查：
1. Python 版本和依赖包是否正确安装
2. Windows Live Caption 是否正常工作
3. 脚本输出的错误信息
4. 系统权限设置
5. 网络连接状态（翻译功能）
