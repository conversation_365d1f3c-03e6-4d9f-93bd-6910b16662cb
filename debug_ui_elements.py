#!/usr/bin/env python3
"""
调试脚本：探索Live Caption UI元素的auto_id
用于找到按钮和菜单项的正确auto_id，避免依赖文本标题
"""

import sys
import time
from pywinauto.application import Application
from pywinauto.keyboard import send_keys

def connect_to_livecaption():
    """连接到Live Caption窗口"""
    try:
        app = Application(backend='uia').connect(path="LiveCaptions.exe")
        window = app.top_window()
        return app, window
    except Exception as e:
        print(f"无法连接到Live Caption: {e}")
        return None, None

def explore_settings_menu(window):
    """探索设置菜单中的元素"""
    print("🔍 探索设置菜单...")
    
    try:
        # 点击设置按钮
        settings_button = window.child_window(auto_id="SettingsButton", control_type="Button")
        if not settings_button.exists(timeout=5):
            print("❌ 设置按钮不存在")
            return
        
        settings_button.click()
        time.sleep(1)  # 等待菜单出现
        
        # 尝试找到弹出窗口
        print("\n📋 查找弹出窗口...")
        
        # 方法1: 通过标题查找
        try:
            popup_pane = window.child_window(title="主机弹出窗口", control_type="Pane")
            if popup_pane.exists():
                print("✅ 找到弹出窗口 (通过标题)")
                print_control_structure(popup_pane, "弹出窗口")
            else:
                print("❌ 未找到弹出窗口 (通过标题)")
        except Exception as e:
            print(f"❌ 通过标题查找弹出窗口失败: {e}")
        
        # 方法2: 通过控件类型查找所有Pane
        print("\n📋 查找所有Pane控件...")
        try:
            panes = window.children(control_type="Pane")
            for i, pane in enumerate(panes):
                if pane.is_visible():
                    print(f"\n--- Pane {i+1} ---")
                    print(f"标题: '{pane.window_text()}'")
                    print(f"auto_id: '{pane.automation_id()}'")
                    print(f"class_name: '{pane.class_name()}'")
                    
                    # 查找其中的菜单项
                    try:
                        menu_items = pane.children(control_type="MenuItem")
                        if menu_items:
                            print(f"包含 {len(menu_items)} 个菜单项:")
                            for j, item in enumerate(menu_items):
                                print(f"  菜单项 {j+1}: '{item.window_text()}' (auto_id: '{item.automation_id()}')")
                    except Exception:
                        pass
        except Exception as e:
            print(f"❌ 查找Pane控件失败: {e}")
        
        # 关闭菜单
        send_keys("{ESC}")
        
    except Exception as e:
        print(f"❌ 探索设置菜单失败: {e}")

def explore_language_dialog(window):
    """探索语言设置对话框"""
    print("\n🌍 探索语言设置对话框...")
    
    try:
        # 打开设置菜单
        settings_button = window.child_window(auto_id="SettingsButton", control_type="Button")
        settings_button.click()
        time.sleep(1)
        
        # 点击更改语言
        change_language_item = window.child_window(auto_id="ChangeLanguageMenuFlyoutItem", control_type="MenuItem")
        if change_language_item.exists():
            change_language_item.invoke()
            time.sleep(2)  # 等待语言对话框出现
            
            print("📋 查找语言对话框中的按钮...")
            
            # 查找所有按钮
            buttons = window.descendants(control_type="Button")
            print(f"找到 {len(buttons)} 个按钮:")
            
            for i, btn in enumerate(buttons):
                if btn.is_visible():
                    text = btn.window_text().strip()
                    auto_id = btn.automation_id()
                    class_name = btn.class_name()
                    
                    print(f"  按钮 {i+1}:")
                    print(f"    文本: '{text}'")
                    print(f"    auto_id: '{auto_id}'")
                    print(f"    class_name: '{class_name}'")
                    print(f"    是否启用: {btn.is_enabled()}")
                    print()
            
            # 按ESC关闭
            send_keys("{ESC}")
            send_keys("{ESC}")
        else:
            print("❌ 未找到更改语言菜单项")
            send_keys("{ESC}")
            
    except Exception as e:
        print(f"❌ 探索语言对话框失败: {e}")

def print_control_structure(control, name, max_depth=2, current_depth=0):
    """打印控件结构"""
    if current_depth >= max_depth:
        return
    
    indent = "  " * current_depth
    try:
        text = control.window_text()
        auto_id = control.automation_id()
        control_type = control.control_type()
        
        print(f"{indent}{name}:")
        print(f"{indent}  文本: '{text}'")
        print(f"{indent}  auto_id: '{auto_id}'")
        print(f"{indent}  类型: {control_type}")
        
        # 递归打印子控件
        try:
            children = control.children()
            for i, child in enumerate(children):
                if child.is_visible():
                    print_control_structure(child, f"子控件{i+1}", max_depth, current_depth + 1)
        except Exception:
            pass
            
    except Exception as e:
        print(f"{indent}❌ 无法获取控件信息: {e}")

def main():
    print("🔧 Live Caption UI元素调试工具")
    print("=" * 50)
    print("此工具将探索Live Caption的UI元素，找到正确的auto_id")
    print("请确保Live Caption已经运行并且窗口可见")
    print("=" * 50)
    
    input("按 Enter 键开始探索...")
    
    app, window = connect_to_livecaption()
    if not window:
        print("❌ 无法连接到Live Caption，请确保应用正在运行")
        return
    
    print("✅ 成功连接到Live Caption")
    
    try:
        # 探索设置菜单
        explore_settings_menu(window)
        
        time.sleep(2)
        
        # 探索语言设置对话框
        explore_language_dialog(window)
        
        print("\n" + "=" * 50)
        print("🎯 探索完成！")
        print("请查看上面的输出，找到正确的auto_id来替换硬编码的文本")
        
    except Exception as e:
        print(f"❌ 探索过程中发生错误: {e}")

if __name__ == "__main__":
    main()
