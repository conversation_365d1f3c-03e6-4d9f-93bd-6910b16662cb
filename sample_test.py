import time
from win_offline_asr import start_livecaptions, set_language_by_prefix,list_languages
def main():
    total_start = time.time()

    print(">>> Starting LiveCaptions...")
    t1 = time.time()
    start_livecaptions()
    t2 = time.time()
    print(f"Start: {t2 - t1:.2f}s")

    print(">>> Setting language to English...")
    t3 = time.time()
    #set_language_by_prefix("日语")
    print(list_languages())
    t4 = time.time()
    print(f"Set Language: {t4 - t3:.2f}s")

    print(f"Total: {t4 - total_start:.2f}s")

if __name__ == "__main__":
    main()
