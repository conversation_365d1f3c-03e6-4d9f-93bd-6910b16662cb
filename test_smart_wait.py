#!/usr/bin/env python3
"""
测试智能等待功能的简单脚本
用于验证 win-offline-asr.py 中的智能等待改进是否有效
"""

import sys
import time
import subprocess
import json

def test_basic_functionality():
    """测试基本功能是否正常工作"""
    print("测试基本功能...")
    
    # 测试启动
    print("1. 测试启动 Live Caption...")
    result = subprocess.run([sys.executable, "win-offline-asr.py", "start"], 
                          capture_output=True, text=True)
    if result.returncode == 0:
        response = json.loads(result.stdout)
        print(f"   启动结果: {response}")
    else:
        print(f"   启动失败: {result.stderr}")
        return False
    
    # 等待一下让应用完全启动
    time.sleep(2)
    
    # 测试获取字幕
    print("2. 测试获取字幕...")
    result = subprocess.run([sys.executable, "win-offline-asr.py", "get_caption"], 
                          capture_output=True, text=True)
    if result.returncode == 0:
        response = json.loads(result.stdout)
        print(f"   字幕获取结果: {response}")
    else:
        print(f"   字幕获取失败: {result.stderr}")
    
    # 测试语言列表
    print("3. 测试获取语言列表...")
    start_time = time.time()
    result = subprocess.run([sys.executable, "win-offline-asr.py", "list_languages", "--no_translate"], 
                          capture_output=True, text=True, timeout=30)
    end_time = time.time()
    
    if result.returncode == 0:
        try:
            response = json.loads(result.stdout)
            print(f"   语言列表获取成功，耗时: {end_time - start_time:.2f}秒")
            print(f"   找到 {len(response.get('languages', []))} 种语言")
        except json.JSONDecodeError:
            print(f"   响应解析失败: {result.stdout}")
    else:
        print(f"   语言列表获取失败: {result.stderr}")
    
    # 测试停止
    print("4. 测试停止 Live Caption...")
    result = subprocess.run([sys.executable, "win-offline-asr.py", "stop"], 
                          capture_output=True, text=True)
    if result.returncode == 0:
        response = json.loads(result.stdout)
        print(f"   停止结果: {response}")
    else:
        print(f"   停止失败: {result.stderr}")
    
    return True

def test_performance_comparison():
    """测试性能对比（如果可能的话）"""
    print("\n性能测试...")
    print("注意：由于我们已经移除了大部分固定延迟，操作应该更快更可靠")
    
    # 测试快速语言列表获取
    print("测试快速语言列表获取...")
    start_time = time.time()
    result = subprocess.run([sys.executable, "win-offline-asr.py", "list_languages", "--fast", "--no_translate"], 
                          capture_output=True, text=True, timeout=20)
    end_time = time.time()
    
    if result.returncode == 0:
        try:
            response = json.loads(result.stdout)
            print(f"   快速语言列表获取成功，耗时: {end_time - start_time:.2f}秒")
            print(f"   找到 {len(response.get('languages', []))} 种语言")
        except json.JSONDecodeError:
            print(f"   响应解析失败: {result.stdout}")
    else:
        print(f"   快速语言列表获取失败: {result.stderr}")

if __name__ == "__main__":
    print("开始测试智能等待功能...")
    print("=" * 50)
    
    try:
        if test_basic_functionality():
            test_performance_comparison()
        
        print("\n" + "=" * 50)
        print("测试完成！")
        print("\n改进总结:")
        print("1. 移除了大部分固定的 time.sleep() 调用")
        print("2. 使用 pywinauto 的智能等待方法 wait('exists enabled visible', timeout=X)")
        print("3. 减少了滚动操作的延迟时间")
        print("4. 保留了必要的短暂延迟（如监控循环和滚动操作）")
        print("5. 提高了操作的可靠性和响应速度")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
