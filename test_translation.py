#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试微软翻译API功能的脚本
"""

import json
import requests
import uuid

def test_microsoft_translator():
    """测试微软翻译API"""
    
    # 测试语言列表
    test_languages = [
        "简体中文",
        "繁體中文",
        "English",
        "日本語",
        "한국어",
        "Français",
        "Deutsch",
        "Español",
        "Italiano",
        "Português"
    ]
    
    # 构建请求数据
    request_data = [{"text": lang} for lang in test_languages]
    
    # API端点
    url = "https://api.cognitive.microsofttranslator.com/translate?api-version=3.0&to=en"

    # 生成10位随机数作为ClientTraceId
    client_trace_id = str(uuid.uuid4()).replace('-', '')[:10]

    # 请求头
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Ocp-Apim-Subscription-Key': 'f2cb3ca133364ba18781f24a15fc9c8d',
        'Ocp-Apim-Subscription-Region': 'centralus',
        'X-ClientTraceId': client_trace_id
    }
    
    try:
        print("正在测试微软翻译API...")
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(request_data, ensure_ascii=False)}")
        
        # 发送请求
        response = requests.post(url, headers=headers, json=request_data, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            # 解析响应
            translations = response.json()
            print(f"翻译结果:")
            
            # 构建映射字典
            result = {}
            for i, translation in enumerate(translations):
                if i < len(test_languages):
                    original = test_languages[i]
                    translated = translation['translations'][0]['text']
                    result[original] = translated
                    print(f"  {original} -> {translated}")
            
            return result
        else:
            print(f"请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"网络请求失败: {e}")
        return None
    except Exception as e:
        print(f"发生未知错误: {e}")
        return None

if __name__ == "__main__":
    result = test_microsoft_translator()
    if result:
        print("\n翻译成功!")
        print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        print("\n翻译失败!")
